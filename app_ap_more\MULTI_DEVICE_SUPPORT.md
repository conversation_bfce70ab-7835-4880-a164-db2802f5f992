# ESP8266 多设备支持功能说明

## 概述

本版本的ESP8266固件已升级为支持多设备连接，可以同时与最多8个设备进行UDP通信。

## 主要特性

### 1. 多设备管理
- 支持最多8个设备同时连接
- 每个设备有独立的UDP连接和缓冲区
- 自动设备发现和注册机制
- 设备超时检测和状态管理

### 2. 设备状态
- `DISCONNECTED`: 设备未连接
- `DISCOVERING`: 设备发现中
- `CONNECTED`: 设备已连接
- `ACTIVE`: 设备活跃状态
- `TIMEOUT`: 设备超时

### 3. 网络配置
- 设备发现端口: 8889
- 数据传输端口: 8888
- 设备超时时间: 30秒

## 使用方法

### 1. 设备发现
ESP8266启动后会自动广播设备发现请求。设备也可以通过SPI命令手动触发发现：

**SPI命令格式:**
```
第一个字节: 0x0E (ESP8266命令标识)
第二个字节: 0x07 (设备管理命令)
第三个字节: 0x01 (设备发现子命令)
```

### 2. 设置活跃设备
可以通过SPI命令设置当前活跃设备：

**SPI命令格式:**
```
第一个字节: 0x0E (ESP8266命令标识)
第二个字节: 0x07 (设备管理命令)
第三个字节: 0x02 (设置活跃设备子命令)
第四个字节: 设备ID (0-7)
```

### 3. 查看设备列表
通过SPI命令查看当前连接的设备：

**SPI命令格式:**
```
第一个字节: 0x0E (ESP8266命令标识)
第二个字节: 0x07 (设备管理命令)
第三个字节: 0x03 (获取设备列表子命令)
```

### 4. 数据发送
数据发送支持两种模式：

#### 向活跃设备发送
默认情况下，数据会发送到当前活跃设备。如果没有活跃设备，会使用原有的UDP连接。

#### 向指定设备发送
在SPI数据帧中指定目标设备ID：

**SPI命令格式 (0x0A类型):**
```
第一个字节: 0x0A (发送给APP的指令)
第二个字节: 目标设备ID (0-7, 0xFF表示广播)
后续字节: 数据内容
```

## API函数

### 设备管理函数
- `device_manager_init()`: 初始化设备管理器
- `device_register()`: 注册新设备
- `device_find_by_ip()`: 根据IP查找设备
- `device_find_by_id()`: 根据ID查找设备
- `device_update_activity()`: 更新设备活跃时间

### 数据传输函数
- `device_send_data()`: 向指定设备发送数据
- `device_broadcast_data()`: 向所有设备广播数据
- `device_send_to_target()`: 向目标设备发送数据

### 发现和管理函数
- `device_broadcast_discovery()`: 广播设备发现请求
- `device_get_active_device()`: 获取当前活跃设备
- `device_check_timeouts()`: 检查设备超时

## 兼容性

本版本保持与原有单设备版本的向后兼容性：
- 原有的UDP连接仍然可用
- 原有的SPI命令格式仍然支持
- 如果没有多设备连接，系统会自动回退到原有模式

## 调试信息

启用调试模式后，系统会输出以下信息：
- 设备注册和连接状态
- 数据发送统计
- 设备超时检测
- 设备发现过程

## 注意事项

1. 最大设备数量限制为8个
2. 每个设备有独立的8KB发送缓冲区
3. 设备超时时间为30秒，可根据需要调整
4. 建议在网络稳定的环境下使用多设备功能
5. 设备发现使用UDP广播，确保网络支持广播包

## 故障排除

### 设备无法发现
1. 检查网络连接
2. 确认设备在同一网段
3. 检查防火墙设置
4. 验证UDP端口8889是否开放

### 数据发送失败
1. 检查目标设备是否在线
2. 验证设备ID是否正确
3. 检查网络延迟和丢包情况
4. 确认设备缓冲区未满

### 设备频繁超时
1. 检查网络稳定性
2. 调整超时时间设置
3. 优化数据发送频率
4. 检查设备负载情况
