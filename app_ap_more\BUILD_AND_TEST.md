# ESP8266 多设备支持 - 编译和测试指南

## 编译步骤

### 1. 环境准备
确保已安装ESP8266 NONOS SDK开发环境。

### 2. 编译固件
```bash
cd app_ap_more
make clean
make
```

### 3. 生成固件文件
```bash
# 生成用户固件
./gen_misc.sh
```

### 4. 烧录固件
使用ESP8266烧录工具将生成的固件烧录到设备。

## 测试步骤

### 1. 基本功能测试

#### 启动测试
1. 烧录固件并重启ESP8266
2. 观察串口输出，应该看到：
   ```
   Device manager initialized, max devices: 8
   Device discovery broadcast sent
   ```

#### 设备发现测试
1. 在同一网络中启动测试设备
2. 测试设备应发送UDP包到端口8889：
   ```
   "Are You ESP8266 Device?"
   ```
3. ESP8266应回复：
   ```
   "Yes,I'm ESP8266!"
   ```
4. 观察串口输出设备注册信息

### 2. 多设备连接测试

#### 连接多个设备
1. 启动2-3个测试设备
2. 每个设备发送发现请求
3. 观察ESP8266串口输出，应显示多个设备注册

#### 验证设备状态
通过SPI发送设备列表命令：
```
SPI数据: 0x0E 0x07 0x03 [其他字节填0]
```

### 3. 数据传输测试

#### 单设备数据发送
1. 通过SPI发送数据命令：
   ```
   SPI数据: 0x0A 0x00 [数据内容...]
   ```
2. 验证数据是否发送到活跃设备

#### 指定设备数据发送
1. 设置活跃设备：
   ```
   SPI数据: 0x0E 0x07 0x02 0x01 [其他字节填0]
   ```
2. 发送数据到指定设备：
   ```
   SPI数据: 0x0A 0x01 [数据内容...]
   ```

#### 广播数据测试
1. 发送广播数据：
   ```
   SPI数据: 0x0A 0xFF [数据内容...]
   ```
2. 验证所有连接设备都收到数据

### 4. 超时和恢复测试

#### 设备超时测试
1. 断开一个测试设备的网络连接
2. 等待30秒以上
3. 观察ESP8266输出设备超时信息

#### 设备恢复测试
1. 重新连接测试设备网络
2. 设备应自动重新注册
3. 验证数据传输恢复正常

## 测试工具

### 1. UDP测试客户端
可以使用以下Python脚本作为测试设备：

```python
import socket
import time

# 设备发现
def device_discovery():
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
    
    # 发送发现请求
    message = "Are You ESP8266 Device?"
    sock.sendto(message.encode(), ('***************', 8889))
    
    # 等待响应
    sock.settimeout(5)
    try:
        data, addr = sock.recvfrom(1024)
        print(f"Response from {addr}: {data.decode()}")
        return addr[0]
    except socket.timeout:
        print("No response received")
        return None
    finally:
        sock.close()

# 数据接收
def data_receiver(esp_ip):
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.bind(('', 8888))
    
    print(f"Listening for data from {esp_ip}...")
    while True:
        try:
            data, addr = sock.recvfrom(8192)
            if addr[0] == esp_ip:
                print(f"Received {len(data)} bytes from ESP8266")
        except KeyboardInterrupt:
            break
    
    sock.close()

if __name__ == "__main__":
    esp_ip = device_discovery()
    if esp_ip:
        data_receiver(esp_ip)
```

### 2. SPI测试工具
如果有SPI主机设备，可以发送以下测试命令：

```c
// 设备发现命令
uint8_t discovery_cmd[] = {0x0E, 0x07, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, ...};

// 设置活跃设备命令
uint8_t set_active_cmd[] = {0x0E, 0x07, 0x02, 0x01, 0x00, 0x00, 0x00, 0x00, ...};

// 获取设备列表命令
uint8_t list_devices_cmd[] = {0x0E, 0x07, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, ...};

// 发送数据到设备1
uint8_t send_data_cmd[] = {0x0A, 0x01, 0x12, 0x34, 0x56, 0x78, 0x00, 0x00, ...};
```

## 性能测试

### 1. 吞吐量测试
- 测试单设备最大数据传输速率
- 测试多设备并发传输性能
- 记录丢包率和延迟

### 2. 稳定性测试
- 长时间运行测试（24小时以上）
- 频繁连接断开测试
- 网络异常恢复测试

### 3. 资源使用测试
- 内存使用情况监控
- CPU使用率测试
- 网络带宽占用测试

## 常见问题

### 编译错误
1. 检查SDK版本兼容性
2. 确认所有头文件路径正确
3. 验证Makefile配置

### 运行时错误
1. 检查内存分配是否成功
2. 验证网络配置
3. 确认SPI通信正常

### 性能问题
1. 调整缓冲区大小
2. 优化数据发送频率
3. 检查网络质量

## 调试技巧

1. 启用调试模式查看详细日志
2. 使用网络抓包工具分析UDP通信
3. 监控ESP8266内存使用情况
4. 检查SPI通信时序
